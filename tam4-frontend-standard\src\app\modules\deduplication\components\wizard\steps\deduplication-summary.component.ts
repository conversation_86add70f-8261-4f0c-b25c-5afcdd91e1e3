import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { SelectorMap } from '@creactives/models';
import { Tam4TranslationService } from '@creactives/tam4-translation-core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TableModule } from 'primeng/table';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from 'src/app/components';
import { SortByPrimaryPipe } from '../../../deduplication.pipe';
import { Subtype } from '../../../models/deduplication.models';
import { DeduplicationSelectors } from '../../../store/deduplication.selectors';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'deduplicationRequest', selector: DeduplicationSelectors.getDeduplicationRequest},
  {key: 'enrichedPlantData', selector: DeduplicationSelectors.getEnrichedPlantData}
];

@Component({
  selector: 'deduplication-summary',
  template: `
    <div class="deduplication-summary">
      <p-table [value]="signals?.deduplicationRequest().materialsDeduplication | sortByPrimary" class="table m-table">
        <ng-template pTemplate="header" let-material>
          <tr>
            <th>
              {{'layout.relationship-popup.materials-code' | translate}}
            </th>
            <th>
              {{'layout.relationship-popup.material-description' | translate}}
            </th>
            <th>
              {{'layout.relationship-popup.md-statuses' | translate }}
            </th>
            <th>
              {{'deduplication.steps.summary.tableColumn.relationshipType' | translate}}
            </th>
            <th>
              {{'deduplication.steps.summary.tableColumn.subtype' | translate}}
            </th>
            <th></th>
            <th></th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-material>
          <tr>
            <td>
                #{{material.client}} / {{material.materialCode}}
            </td>
            <td>
                {{material.description}}
            </td>
            <td>
                {{material.status}} - {{material.statusDescription}}
            </td>
            <td>
                {{signals?.deduplicationRequest().relationshipType}}
            </td>
            <td>
                {{getSubtypeValue(signals?.deduplicationRequest().relationshipSubType)}}
            </td>
            <td>
                <span *ngIf="material.completeness" class="m-badge m-badge--danger m-badge--wide completeness completeness-{{ material.completeness | lowercase}}">
                  {{ material.completeness }}
                </span>
            </td>
            <td>
              <label class="fancy-checkbox">
                <input
                    type="checkbox"
                    [disabled]="true"
                    [checked]="material.primary"
                />
                <span></span>
              </label>
            </td>
          </tr>
        </ng-template>
      </p-table>
      @if (signals?.enrichedPlantData()) {
        {{'deduplication.steps.summary.plantChangesTitle' | translate}}
        <p-table [value]="">
          <ng-template pTemplate="header">
            <tr>
              <th>
                {{'deduplication.steps.summary.tableColumn.plantCode' | translate}}
              </th>
              <th>
                {{'deduplication.steps.summary.tableColumn.plantDescription' | translate}}
              </th>
              <th>
                {{'deduplication.steps.summary.tableColumn.plantChangeType' | translate}}
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-plant>
            <tr>
              <td>
                {{plant.code}}
              </td>
              <td>
                {{plant.description}}
              </td>
              <td>
                {{plant.changeType}}
              </td>
            </tr>
          </ng-template>
        </p-table>
      }
    </div>
  `,
  styleUrls: ['md-selector/relationship-md-selector.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FontAwesomeModule,
    TranslateModule,
    TableModule,
    ReactiveFormsModule,
    SortByPrimaryPipe
]
})
export class DeduplicationSummaryComponent extends TamAbstractReduxComponent<SelectorMap>{
  @Input() mdDomain: string;

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store
  ) {
      super(translate, tamTranslate, store, storeSelectors);
  }

  getMdStatusDescription(materialClient: string, statusKey: string) {
    const statuses = this.signals?.availableStatusesByClient();
    const statusesByClient = statuses[materialClient];
    return statusesByClient.find(entry => entry.key === statusKey) ? statusesByClient.find(entry => entry.key === statusKey).value : statusKey;
  }

  getSubtypeValue(subtype: Subtype) {
    return subtype.value;
  }

}
